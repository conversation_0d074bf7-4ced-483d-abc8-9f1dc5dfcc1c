# UI预览功能说明

## 功能概述

商家配置管理页面的UI预览功能已经升级，现在可以更真实地模拟uniapp小程序的实际显示效果。

## 主要改进

### 1. 更真实的手机预览界面
- 添加了手机状态栏（时间、信号、电池图标）
- 模拟真实的手机边框和圆角设计
- 增加了阴影效果，更具立体感

### 2. 完整的页面结构预览
- **导航栏**：显示页面标题，支持主题色彩自定义
- **主要内容区域**：
  - 商家Logo显示
  - 商家名称和地址信息
  - 桌台信息展示
  - 背景渐变色效果（基于主题色自动生成）
- **底部功能按钮**：点餐和抽奖按钮，带有渐变色效果

### 3. 欢迎遮罩层预览
- 支持切换显示/隐藏欢迎遮罩层
- 显示欢迎图片和欢迎语
- 模拟倒计时自动关闭效果

### 4. 动态配置应用
- 主题色彩实时预览
- 背景图片效果展示
- Logo图片显示
- 欢迎语文本预览
- 页面标题动态更新

## 技术实现

### 计算属性
```javascript
computed: {
  // 计算主题色彩
  computedPrimaryColor() {
    return this.uiConfig.primaryColor || '#667eea'
  },
  
  // 计算背景渐变色（模拟uniapp的实现）
  computedBackgroundGradient() {
    const color = this.computedPrimaryColor
    const darkerColor = this.adjustColor(color, -20)
    return `linear-gradient(135deg, ${color} 0%, ${darkerColor} 100%)`
  }
}
```

### 颜色调整方法
```javascript
// 调整颜色亮度（模拟uniapp的adjustColor方法）
adjustColor(color, amount) {
  // 实现颜色亮度调整逻辑
}
```

## 使用方法

1. 在商家配置管理页面，切换到"UI配置"标签页
2. 配置相关的UI参数：
   - 主题色彩
   - 欢迎语
   - 页面标题
   - 活动弹窗图
   - 商家Logo
3. 点击"预览效果"按钮查看实时预览
4. 在预览对话框中可以：
   - 查看基本的页面布局效果
   - 点击"显示欢迎层"按钮预览欢迎遮罩层效果
   - 点击"隐藏欢迎层"按钮关闭欢迎遮罩层

## 预览效果特点

- **真实性**：高度还原uniapp小程序的实际显示效果
- **交互性**：支持切换不同的预览状态
- **响应性**：配置修改后可立即在预览中看到效果
- **完整性**：包含了页面的所有主要元素和布局

## 注意事项

1. 预览效果基于当前的配置数据实时生成
2. 图片资源需要确保可以正常访问
3. 颜色配置支持标准的十六进制颜色值
4. 预览界面尺寸固定为320x600像素，模拟常见的手机屏幕比例
