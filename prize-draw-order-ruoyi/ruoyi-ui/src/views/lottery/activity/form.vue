<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>基本信息</span>
        </div>
        <el-row>
          <el-col :span="12">
            <el-form-item label="活动名称" prop="activityName">
              <el-input v-model="form.activityName" placeholder="请输入活动名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{
                  dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startTime">
              <el-date-picker v-model="form.startTime" type="datetime" placeholder="选择开始时间"
                value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间" prop="endTime">
              <el-date-picker v-model="form.endTime" type="datetime" placeholder="选择结束时间"
                value-format="yyyy-MM-dd HH:mm:ss" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="每日抽奖次数" prop="dailyLimit">
              <el-input-number v-model="form.dailyLimit" :min="1" :max="100" placeholder="每个用户每天可抽奖的次数"
                style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="总抽奖次数" prop="totalLimit">
              <el-input-number v-model="form.totalLimit" :min="0" placeholder="0表示不限制" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="活动描述" prop="activityDesc">
          <el-input v-model="form.activityDesc" type="textarea" :rows="3" placeholder="请输入活动描述" />
        </el-form-item>
        <el-form-item label="抽奖规则" prop="drawRules">
          <el-input v-model="form.drawRules" type="textarea" :rows="3" placeholder="请输入抽奖规则说明" />
        </el-form-item>
        <el-form-item label="领取说明" prop="claimInstruction">
          <el-input v-model="form.claimInstruction" type="textarea" :rows="3" placeholder="请输入奖品领取说明，用户中奖后会看到此说明" />
        </el-form-item>
        <el-form-item label="微信二维码" prop="wechatQrcode">
          <image-upload v-model="form.wechatQrcode" />
        </el-form-item>
      </el-card>

      <el-card class="box-card" style="margin-top: 20px">
        <div slot="header" class="clearfix">
          <span>奖品配置</span>
          <el-button style="float: right; padding: 3px 0" type="text" @click="addPrize">添加奖品</el-button>
        </div>
        <el-table :data="prizeList" border style="width: 100%">
          <el-table-column prop="prizeName" label="奖品名称" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.prizeName" placeholder="奖品名称" />
            </template>
          </el-table-column>
          <el-table-column prop="prizeType" label="奖品类型">
            <template slot-scope="scope">
              <el-select v-model="scope.row.prizeType" placeholder="选择类型">
                <el-option label="实物奖品" value="physical" />
                <el-option label="优惠券" value="coupon" />
                <el-option label="积分" value="points" />
                <el-option label="谢谢参与" value="thanks" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="prizeValue" label="奖品价值">
            <template slot-scope="scope">
              <el-input v-model="scope.row.prizeValue" placeholder="奖品价值" />
            </template>
          </el-table-column>
          <el-table-column prop="probability" label="中奖概率(%)">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.probability" :min="0" :max="100" :precision="2" style="width: 100%" />
            </template>
          </el-table-column>
          <el-table-column prop="totalCount" label="奖品总数">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.totalCount" :min="0" placeholder="0为不限制" style="width: 100%" />
            </template>
          </el-table-column>
          <el-table-column prop="dailyCount" label="每日限量">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.dailyCount" :min="0" placeholder="0为不限制" style="width: 100%" />
            </template>
          </el-table-column>
          <el-table-column prop="prizeImage" label="奖品图片">
            <template slot-scope="scope">
              <ImageUpload v-model="scope.row.prizeImage" :limit="1" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="removePrize(scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 10px; color: #909399; font-size: 12px">
          <p>注意：所有奖品的中奖概率总和应该等于100%</p>
          <p>当前概率总和：{{ totalProbability }}%</p>
        </div>
      </el-card>

      <div style="margin-top: 20px; text-align: center">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getLotteryActivity, addLotteryActivity, updateLotteryActivity } from "@/api/lottery/activity";
import { getMerchantId } from "@/api/login";

export default {
  name: "LotteryActivityForm",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 表单参数
      form: {
        activityId: null,
        merchantId: null,
        activityName: null,
        activityDesc: null,
        startTime: null,
        endTime: null,
        prizeConfig: null,
        drawRules: null,
        dailyLimit: 3,
        totalLimit: 0,
        status: "0",
        claimInstruction: null,
        wechatQrcode: null,
        remark: null
      },
      // 奖品列表
      prizeList: [],
      // 表单校验
      rules: {
        activityName: [
          { required: true, message: "活动名称不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "change" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "change" }
        ],
        dailyLimit: [
          { required: true, message: "每日抽奖次数不能为空", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    totalProbability() {
      return this.prizeList.reduce((sum, prize) => sum + (prize.probability || 0), 0);
    }
  },
  created() {
    this.initData();
  },
  methods: {
    async initData() {
      // 获取商家ID
      try {
        const res = await getMerchantId();
        if (res.code === 200) {
          this.form.merchantId = res.data;
        }
      } catch (error) {
        console.error('获取商家ID失败:', error);
      }

      // 如果是编辑模式，获取活动信息
      const activityId = this.$route.params && this.$route.params.activityId;
      if (activityId) {
        this.getActivity(activityId);
      } else {
        // 新增模式，添加默认奖品
        this.addDefaultPrizes();
      }
    },
    /** 获取活动信息 */
    getActivity(activityId) {
      getLotteryActivity(activityId).then(response => {
        this.form = response.data;
        if (this.form.prizeConfig) {
          this.prizeList = JSON.parse(this.form.prizeConfig);
        } else {
          this.addDefaultPrizes();
        }
      });
    },
    /** 添加默认奖品 */
    addDefaultPrizes() {
      this.prizeList = [
        {
          prizeName: "一等奖",
          prizeType: "physical",
          prizeValue: "100元代金券",
          probability: 5,
          totalCount: 10,
          dailyCount: 2,
          prizeImage: ""
        },
        {
          prizeName: "二等奖",
          prizeType: "coupon",
          prizeValue: "50元代金券",
          probability: 15,
          totalCount: 50,
          dailyCount: 10,
          prizeImage: ""
        },
        {
          prizeName: "谢谢参与",
          prizeType: "thanks",
          prizeValue: "谢谢参与",
          probability: 80,
          totalCount: 0,
          dailyCount: 0,
          prizeImage: ""
        }
      ];
    },
    /** 添加奖品 */
    addPrize() {
      this.prizeList.push({
        prizeName: "",
        prizeType: "physical",
        prizeValue: "",
        probability: 0,
        totalCount: 0,
        dailyCount: 0,
        prizeImage: ""
      });
    },
    /** 删除奖品 */
    removePrize(index) {
      this.prizeList.splice(index, 1);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 验证奖品配置
          if (this.prizeList.length === 0) {
            this.$modal.msgError("请至少配置一个奖品");
            return;
          }

          // 验证概率总和
          if (Math.abs(this.totalProbability - 100) > 0.01) {
            this.$modal.msgError("所有奖品的中奖概率总和必须等于100%");
            return;
          }

          // 验证时间
          if (new Date(this.form.startTime) >= new Date(this.form.endTime)) {
            this.$modal.msgError("结束时间必须大于开始时间");
            return;
          }

          this.form.prizeConfig = JSON.stringify(this.prizeList);

          if (this.form.activityId != null) {
            updateLotteryActivity(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.$router.push("/lottery/activity");
            });
          } else {
            addLotteryActivity(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.$router.push("/lottery/activity");
            });
          }
        }
      });
    },
    /** 取消按钮 */
    cancel() {
      this.$router.push("/lottery/activity");
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}
</style>
