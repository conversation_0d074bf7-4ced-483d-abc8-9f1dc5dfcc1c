<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>商家配置管理</span>
      </div>
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 基本配置 -->
        <el-tab-pane label="基本配置" name="basic">
          <el-form ref="basicForm" :model="basicConfig" :rules="basicRules" label-width="120px">
            <el-form-item label="商家名称" prop="merchantName">
              <el-input v-model="basicConfig.merchantName" placeholder="请输入商家名称" />
            </el-form-item>
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="basicConfig.contactPhone" placeholder="请输入联系电话" />
            </el-form-item>
            <el-form-item label="商家地址" prop="address">
              <el-input v-model="basicConfig.address" type="textarea" placeholder="请输入商家地址" />
            </el-form-item>

            <el-form-item>
              <el-button type="primary" @click="saveBasicConfig">保存基本配置</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>



        <!-- UI配置 -->
        <el-tab-pane label="UI配置" name="ui">
          <el-form ref="uiForm" :model="uiConfig" label-width="120px">
            <el-form-item label="主题色彩">
              <el-color-picker v-model="uiConfig.primaryColor" />
            </el-form-item>
            <el-form-item label="欢迎语">
              <el-input v-model="uiConfig.welcomeText" placeholder="请输入首页欢迎语" />
            </el-form-item>
            <el-form-item label="页面标题">
              <el-input v-model="uiConfig.pageTitle" placeholder="请输入小程序页面标题" />
            </el-form-item>
            <el-form-item label="活动弹窗图">
              <image-upload v-model="uiConfig.backgroundImage" :limit="1" />
            </el-form-item>
            <el-form-item label="商家Logo">
              <image-upload v-model="uiConfig.logoImage" :limit="1" />
            </el-form-item>


            <el-form-item>
              <el-button type="primary" @click="saveUIConfig">保存UI配置</el-button>
              <el-button type="primary" @click="previewUI">
                预览效果
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>


      </el-tabs>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog title="UI预览效果" :visible.sync="previewVisible" width="450px" :close-on-click-modal="false">
      <div class="preview-container">
        <div class="preview-phone">
          <!-- 手机状态栏 -->
          <div class="preview-status-bar">
            <span class="status-time">9:41</span>
            <span class="status-signal">📶 📶 🔋</span>
          </div>

          <!-- 导航栏 -->
          <div class="preview-header" :style="{ backgroundColor: computedPrimaryColor }">
            <span>{{ uiConfig.pageTitle || '抽奖点餐' }}</span>
          </div>

          <!-- 主要内容区域 -->
          <div class="preview-content" :style="{ background: computedBackgroundGradient }">
            <!-- 商家信息卡片 -->
            <div class="preview-merchant-info">
              <!-- Logo -->
              <div class="preview-logo" v-if="uiConfig.logoImage">
                <img :src="uiConfig.logoImage" alt="Logo" />
              </div>

              <!-- 商家信息 -->
              <div class="preview-merchant-name">{{ basicConfig.merchantName || '示例商家' }}</div>
              <div class="preview-merchant-address" v-if="basicConfig.address">{{ basicConfig.address }}</div>
              <div class="preview-table-info">
                <span class="preview-table-number">桌台：A001</span>
              </div>
            </div>
          </div>

          <!-- 底部功能按钮 -->
          <div class="preview-bottom-buttons">
            <div class="preview-function-btn order-btn">
              <div class="preview-btn-icon">🍽️</div>
              <div class="preview-btn-text">点餐</div>
            </div>
            <div class="preview-function-btn lottery-btn">
              <div class="preview-btn-icon">🎁</div>
              <div class="preview-btn-text">抽奖</div>
            </div>
          </div>
        </div>

        <!-- 欢迎遮罩层预览 -->
        <div class="preview-welcome-overlay" v-if="showWelcomePreview">
          <div class="preview-overlay-content">
            <div class="preview-overlay-image-container" v-if="uiConfig.backgroundImage">
              <span v-if="uiConfig.welcomeText" class="preview-welcome-text">{{ uiConfig.welcomeText }}</span>
              <img :src="uiConfig.backgroundImage" class="preview-overlay-image" alt="欢迎图片" />
              <span class="preview-countdown-text">10秒后自动关闭</span>
            </div>
            <div v-else-if="uiConfig.welcomeText" class="preview-welcome-only">
              <span class="preview-welcome-text">{{ uiConfig.welcomeText }}</span>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关闭</el-button>
        <el-button type="primary" @click="toggleWelcomePreview">
          {{ showWelcomePreview ? '隐藏欢迎层' : '显示欢迎层' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMerchantConfigMapByMerchantId, batchSaveOrUpdateConfig } from "@/api/system/merchantConfig"
import { getMerchant, updateMerchant } from "@/api/system/merchant"
import { getMerchantId } from "@/api/login"
export default {
  name: "MerchantConfig",
  data() {
    return {
      activeTab: 'basic',
      merchantId: null,
      previewVisible: false,
      showWelcomePreview: false,

      // 基本配置
      basicConfig: {
        merchantName: '',
        contactPhone: '',
        address: ''
      },
      basicRules: {
        merchantName: [
          { required: true, message: "商家名称不能为空", trigger: "blur" }
        ]
      },

      // UI配置
      uiConfig: {
        primaryColor: '#667eea',
        backgroundImage: '',
        logoImage: '',
        welcomeText: '欢迎使用抽奖点餐系统',
        pageTitle: '抽奖点餐'
      }
    }
  },

  computed: {
    // 计算主题色彩
    computedPrimaryColor() {
      return this.uiConfig.primaryColor || '#667eea'
    },

    // 计算背景渐变色（模拟uniapp的实现）
    computedBackgroundGradient() {
      const color = this.computedPrimaryColor
      const darkerColor = this.adjustColor(color, -20)
      return `linear-gradient(135deg, ${color} 0%, ${darkerColor} 100%)`
    }
  },



  created() {
    // 这里应该从路由参数或用户信息中获取商家ID
    this.initMerchantId();

  },

  methods: {
    /** 初始化商家ID */
    initMerchantId() {
      getMerchantId().then(response => {
        if (response.data) {
          this.merchantId = response.data;
          this.loadConfigs()
        } else {
          this.$modal.msgError("无法获取商家信息，请联系管理员");
        }
      }).catch(() => {
        this.$modal.msgError("获取商家信息失败");
      });
    },
    async loadConfigs() {
      try {
        // 加载商家基本信息
        await this.loadMerchantInfo()

        // 加载配置信息
        await this.loadMerchantConfigs()
      } catch (error) {
        console.error('加载配置失败:', error)
        this.$modal.msgError("加载配置失败")
      }
    },

    async loadMerchantInfo() {
      const res = await getMerchant(this.merchantId)
      if (res.code === 200) {
        const merchant = res.data
        this.basicConfig = {
          merchantName: merchant.merchantName || '',
          contactPhone: merchant.contactPhone || '',
          address: merchant.address || ''
        }
      }
    },

    async loadMerchantConfigs() {
      const res = await getMerchantConfigMapByMerchantId(this.merchantId)
      if (res.code === 200) {
        const configs = res.data || {}

        // UI配置
        const uiConfigStr = configs.ui_config
        if (uiConfigStr) {
          try {
            const uiConfigObj = JSON.parse(uiConfigStr)
            this.uiConfig = {
              primaryColor: uiConfigObj.primaryColor || '#667eea',
              backgroundImage: uiConfigObj.backgroundImage || '',
              logoImage: uiConfigObj.logoImage || '',
              welcomeText: uiConfigObj.welcomeText || '欢迎使用抽奖点餐系统',
              pageTitle: uiConfigObj.pageTitle || '抽奖点餐'
            }
          } catch (e) {
            console.error('UI配置解析失败:', e)
          }
        }
      }
    },

    async saveBasicConfig() {
      this.$refs["basicForm"].validate(async (valid) => {
        if (valid) {
          try {
            const updateData = {
              merchantId: this.merchantId,
              merchantName: this.basicConfig.merchantName,
              contactPhone: this.basicConfig.contactPhone,
              address: this.basicConfig.address
            }

            await updateMerchant(updateData)
            this.$modal.msgSuccess("基本配置保存成功")
          } catch (error) {
            this.$modal.msgError("保存失败")
          }
        }
      })
    },



    async saveUIConfig() {
      try {
        const configMap = {
          ui_config: JSON.stringify(this.uiConfig)
        }

        await batchSaveOrUpdateConfig(this.merchantId, configMap)
        this.$modal.msgSuccess("UI配置保存成功")
      } catch (error) {
        this.$modal.msgError("保存失败")
      }
    },



    previewUI() {
      this.previewVisible = true
      this.showWelcomePreview = false
    },

    // 切换欢迎层预览
    toggleWelcomePreview() {
      this.showWelcomePreview = !this.showWelcomePreview
    },

    // 调整颜色亮度（模拟uniapp的adjustColor方法）
    adjustColor(color, amount) {
      const usePound = color[0] === '#'
      const col = usePound ? color.slice(1) : color

      const num = parseInt(col, 16)
      let r = (num >> 16) + amount
      let g = (num >> 8 & 0x00FF) + amount
      let b = (num & 0x0000FF) + amount

      r = r > 255 ? 255 : r < 0 ? 0 : r
      g = g > 255 ? 255 : g < 0 ? 0 : g
      b = b > 255 ? 255 : b < 0 ? 0 : b

      return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0')
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-container {
  display: flex;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.preview-phone {
  width: 320px;
  height: 600px;
  border: 3px solid #333;
  border-radius: 25px;
  overflow: hidden;
  background: #000;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
  position: relative;

  // 手机状态栏
  .preview-status-bar {
    height: 25px;
    background: #000;
    color: #fff;
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    .status-time {
      font-weight: bold;
    }

    .status-signal {
      font-size: 10px;
    }
  }

  // 导航栏
  .preview-header {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
  }

  // 主要内容区域
  .preview-content {
    height: 445px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;

    // 商家信息卡片
    .preview-merchant-info {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 15px;
      padding: 25px 20px;
      text-align: center;
      width: 80%;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);

      .preview-logo {
        margin-bottom: 15px;

        img {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: rgba(255, 255, 255, 0.9);
          padding: 5px;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
      }

      .preview-merchant-name {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
      }

      .preview-merchant-address {
        font-size: 12px;
        color: #666;
        margin-bottom: 10px;
        line-height: 1.4;
      }

      .preview-table-info {
        .preview-table-number {
          font-size: 12px;
          color: #888;
          background: rgba(102, 126, 234, 0.1);
          padding: 6px 12px;
          border-radius: 20px;
          display: inline-block;
        }
      }
    }
  }

  // 底部功能按钮
  .preview-bottom-buttons {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 80px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 0 30px;

    .preview-function-btn {
      flex: 1;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      padding: 15px 10px;
      text-align: center;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;
      cursor: pointer;

      &:active {
        transform: scale(0.95);
      }

      .preview-btn-icon {
        font-size: 20px;
        margin-bottom: 3px;
      }

      .preview-btn-text {
        font-size: 11px;
        font-weight: bold;
        color: #fff;
      }

      &.order-btn {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        box-shadow: 0 4px 15px rgba(255, 154, 158, 0.3);
      }

      &.lottery-btn {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        box-shadow: 0 4px 15px rgba(168, 237, 234, 0.3);
      }
    }
  }
}

// 欢迎遮罩层预览
.preview-welcome-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 25px;

  .preview-overlay-content {
    background: transparent;
    border-radius: 15px;
    width: 90%;
    max-width: 280px;
    text-align: center;
    position: relative;

    .preview-welcome-text {
      font-size: 16px;
      color: #ffffff;
      font-weight: bold;
      margin-bottom: 10px;
      display: block;
      text-align: center;
    }

    .preview-countdown-text {
      font-size: 12px;
      color: #ffffff;
      margin-top: 10px;
      display: block;
      text-align: center;
    }

    .preview-overlay-image-container {
      width: 100%;
      background: transparent;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .preview-overlay-image {
        width: 100%;
        height: 250px;
        border-radius: 10px;
        display: block;
        margin: 0 auto;
        object-fit: cover;
      }
    }

    .preview-welcome-only {
      background: rgba(255, 255, 255, 0.95);
      padding: 30px 20px;
      border-radius: 15px;
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(10px);

      .preview-welcome-text {
        color: #333;
        font-size: 18px;
      }
    }
  }
}

.dialog-footer {
  text-align: center;
  padding-top: 10px;
}
</style>
