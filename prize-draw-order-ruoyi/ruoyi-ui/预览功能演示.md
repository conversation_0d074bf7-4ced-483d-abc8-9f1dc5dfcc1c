# 商家配置管理页面 - UI预览功能演示

## 功能概述

本次更新为商家配置管理页面的UI预览功能进行了重大升级，现在可以更真实地模拟uniapp小程序的实际显示效果。

## 主要特性

### 1. 真实的手机界面模拟
- ✅ 手机状态栏（时间、信号、电池图标）
- ✅ 真实的手机边框和圆角设计
- ✅ 立体阴影效果
- ✅ 320x600像素的标准手机屏幕比例

### 2. 完整的页面结构预览
- ✅ **导航栏**：动态显示页面标题，支持主题色彩
- ✅ **主要内容区域**：
  - 商家Logo显示（圆形头像样式）
  - 商家名称和地址信息
  - 桌台信息展示
  - 背景渐变色效果（基于主题色自动生成）
- ✅ **底部功能按钮**：点餐和抽奖按钮，带有渐变色效果

### 3. 欢迎遮罩层预览
- ✅ 支持切换显示/隐藏欢迎遮罩层
- ✅ 显示欢迎图片和欢迎语
- ✅ 模拟倒计时自动关闭效果
- ✅ 半透明黑色背景遮罩

### 4. 动态配置应用
- ✅ 主题色彩实时预览
- ✅ 背景图片效果展示
- ✅ Logo图片显示
- ✅ 欢迎语文本预览
- ✅ 页面标题动态更新

## 使用步骤

### 第一步：配置UI参数
1. 进入商家配置管理页面
2. 切换到"UI配置"标签页
3. 配置以下参数：
   - **主题色彩**：选择主色调（默认：#667eea）
   - **欢迎语**：输入首页欢迎文字
   - **页面标题**：设置小程序页面标题
   - **活动弹窗图**：上传欢迎遮罩层显示的图片
   - **商家Logo**：上传商家标志图片

### 第二步：预览效果
1. 点击"预览效果"按钮
2. 在弹出的预览对话框中查看效果
3. 点击"显示欢迎层"按钮预览欢迎遮罩层
4. 点击"隐藏欢迎层"按钮关闭欢迎遮罩层

### 第三步：保存配置
1. 确认预览效果满意后
2. 点击"保存UI配置"按钮
3. 配置将应用到实际的uniapp小程序中

## 技术亮点

### 1. 颜色自动调整算法
```javascript
// 基于主题色自动生成渐变背景
computedBackgroundGradient() {
  const color = this.computedPrimaryColor
  const darkerColor = this.adjustColor(color, -20)
  return `linear-gradient(135deg, ${color} 0%, ${darkerColor} 100%)`
}
```

### 2. 响应式设计
- 预览界面完全响应配置变化
- 实时更新显示效果
- 无需刷新页面

### 3. 高保真还原
- 完全按照uniapp首页的实际布局设计
- 使用相同的样式和交互效果
- 确保预览效果与实际效果一致

## 预览效果对比

| 配置项 | 预览效果 | 实际效果 |
|--------|----------|----------|
| 主题色彩 | ✅ 导航栏、渐变背景 | ✅ 完全一致 |
| 商家Logo | ✅ 圆形头像显示 | ✅ 完全一致 |
| 欢迎语 | ✅ 遮罩层文字显示 | ✅ 完全一致 |
| 背景图片 | ✅ 遮罩层图片显示 | ✅ 完全一致 |
| 页面标题 | ✅ 导航栏标题 | ✅ 完全一致 |

## 注意事项

1. **图片资源**：确保上传的图片可以正常访问
2. **颜色格式**：支持标准的十六进制颜色值（如：#667eea）
3. **预览尺寸**：预览界面固定为320x600像素
4. **实时更新**：配置修改后立即在预览中生效

## 后续优化建议

1. 添加更多主题色彩预设选项
2. 支持自定义按钮样式配置
3. 增加更多页面元素的配置选项
4. 添加预览效果的导出功能
