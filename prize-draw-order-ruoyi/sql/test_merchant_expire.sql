-- 测试商家过期功能的SQL脚本
-- 用于创建测试数据和验证功能

-- 1. 创建测试商家数据
INSERT INTO `merchant` (
    `merchant_name`, 
    `merchant_code`, 
    `contact_person`, 
    `contact_phone`, 
    `address`, 
    `expire_time`, 
    `status`, 
    `del_flag`, 
    `create_by`, 
    `create_time`
) VALUES 
-- 已过期的商家（状态应该被自动更新为2）
('测试商家A', 'TEST_A', '张三', '13800138001', '测试地址A', '2025-07-30 23:59:59', '0', '0', 'admin', NOW()),
('测试商家B', 'TEST_B', '李四', '13800138002', '测试地址B', '2025-07-31 12:00:00', '0', '0', 'admin', NOW()),

-- 即将到期的商家（7天内）
('测试商家C', 'TEST_C', '王五', '13800138003', '测试地址C', DATE_ADD(NOW(), INTERVAL 3 DAY), '0', '0', 'admin', NOW()),
('测试商家D', 'TEST_D', '赵六', '13800138004', '测试地址D', DATE_ADD(NOW(), INTERVAL 6 DAY), '0', '0', 'admin', NOW()),

-- 正常商家（不会过期）
('测试商家E', 'TEST_E', '孙七', '13800138005', '测试地址E', DATE_ADD(NOW(), INTERVAL 30 DAY), '0', '0', 'admin', NOW()),
('测试商家F', 'TEST_F', '周八', '13800138006', '测试地址F', DATE_ADD(NOW(), INTERVAL 60 DAY), '0', '0', 'admin', NOW());

-- 2. 查询测试数据
SELECT 
    merchant_id,
    merchant_name,
    merchant_code,
    expire_time,
    status,
    CASE 
        WHEN expire_time < NOW() THEN '已过期'
        WHEN expire_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY) THEN '即将过期'
        ELSE '正常'
    END as expire_status,
    CASE status
        WHEN '0' THEN '正常'
        WHEN '1' THEN '停用'
        WHEN '2' THEN '过期'
        ELSE '未知'
    END as status_desc
FROM merchant 
WHERE merchant_code LIKE 'TEST_%'
ORDER BY expire_time;

-- 3. 手动执行过期状态更新（用于测试）
-- UPDATE merchant SET status = '2', update_time = NOW()
-- WHERE status != '2' AND del_flag = '0' 
-- AND expire_time IS NOT NULL 
-- AND expire_time < NOW();

-- 4. 查询过期商家
SELECT 
    merchant_id,
    merchant_name,
    merchant_code,
    expire_time,
    status,
    '已过期' as expire_status
FROM merchant 
WHERE status != '2' AND del_flag = '0' 
AND expire_time IS NOT NULL 
AND expire_time < NOW()
ORDER BY expire_time;

-- 5. 查询即将到期的商家（7天内）
SELECT 
    merchant_id,
    merchant_name,
    merchant_code,
    expire_time,
    status,
    DATEDIFF(expire_time, NOW()) as days_to_expire
FROM merchant 
WHERE status = '0' AND del_flag = '0' 
AND expire_time IS NOT NULL 
AND expire_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
ORDER BY expire_time;

-- 6. 清理测试数据（执行完测试后可以运行）
-- DELETE FROM merchant WHERE merchant_code LIKE 'TEST_%';
