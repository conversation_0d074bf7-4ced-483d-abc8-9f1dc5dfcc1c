-- 验证定时任务配置的SQL脚本

-- 1. 查询商家过期相关的定时任务
SELECT 
    job_id,
    job_name,
    job_group,
    invoke_target,
    cron_expression,
    CASE status
        WHEN '0' THEN '正常'
        WHEN '1' THEN '暂停'
        ELSE '未知'
    END as status_desc,
    create_time,
    remark
FROM sys_job 
WHERE job_name LIKE '%商家%' OR invoke_target LIKE '%merchantTask%'
ORDER BY job_id;

-- 2. 查询最近的任务执行日志
SELECT 
    job_log_id,
    job_name,
    job_group,
    invoke_target,
    job_message,
    CASE status
        WHEN '0' THEN '成功'
        WHEN '1' THEN '失败'
        ELSE '未知'
    END as status_desc,
    exception_info,
    create_time
FROM sys_job_log 
WHERE job_name LIKE '%商家%' OR invoke_target LIKE '%merchantTask%'
ORDER BY create_time DESC
LIMIT 20;

-- 3. 统计商家状态分布
SELECT 
    CASE status
        WHEN '0' THEN '正常'
        WHEN '1' THEN '停用'
        WHEN '2' THEN '过期'
        ELSE '未知'
    END as status_desc,
    COUNT(*) as count
FROM merchant 
WHERE del_flag = '0'
GROUP BY status
ORDER BY status;

-- 4. 查询当前过期的商家
SELECT 
    merchant_id,
    merchant_name,
    merchant_code,
    expire_time,
    status,
    TIMESTAMPDIFF(DAY, expire_time, NOW()) as expired_days
FROM merchant 
WHERE del_flag = '0' 
AND expire_time IS NOT NULL 
AND expire_time < NOW()
ORDER BY expire_time DESC;

-- 5. 查询即将到期的商家（未来7天）
SELECT 
    merchant_id,
    merchant_name,
    merchant_code,
    expire_time,
    status,
    TIMESTAMPDIFF(DAY, NOW(), expire_time) as days_to_expire
FROM merchant 
WHERE del_flag = '0' 
AND expire_time IS NOT NULL 
AND expire_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
ORDER BY expire_time;

-- 6. 检查是否有过期但状态未更新的商家（这些应该被定时任务处理）
SELECT 
    merchant_id,
    merchant_name,
    merchant_code,
    expire_time,
    status,
    TIMESTAMPDIFF(DAY, expire_time, NOW()) as expired_days,
    '需要更新状态' as note
FROM merchant 
WHERE del_flag = '0' 
AND expire_time IS NOT NULL 
AND expire_time < NOW()
AND status != '2';

-- 7. 查询定时任务相关的系统配置
SELECT 
    config_id,
    config_name,
    config_key,
    config_value,
    config_type,
    remark
FROM sys_config 
WHERE config_key LIKE '%job%' OR config_key LIKE '%quartz%' OR config_key LIKE '%schedule%';

-- 8. 显示当前数据库时间（用于确认时间同步）
SELECT NOW() as current_database_time;
