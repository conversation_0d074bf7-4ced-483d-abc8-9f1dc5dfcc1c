-- 商家过期状态检查定时任务配置
-- 每分钟执行一次，检查并更新过期商家状态

-- 检查是否已存在相同的定时任务，如果存在则先删除
DELETE FROM `sys_job` WHERE `job_name` = '商家过期状态检查' OR `job_name` = '商家即将到期提醒';

-- 插入商家过期状态检查定时任务
INSERT INTO `sys_job` (
    `job_id`,
    `job_name`,
    `job_group`,
    `invoke_target`,
    `cron_expression`,
    `misfire_policy`,
    `concurrent`,
    `status`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `remark`
) VALUES (
    100,
    '商家过期状态检查',
    'SYSTEM',
    'merchantTask.checkExpiredMerchants',
    '0 * * * * ?',
    '3',
    '1',
    '0',
    'admin',
    NOW(),
    '',
    NULL,
    '每分钟执行一次，检查并更新过期商家状态为过期'
);

-- 插入商家即将到期提醒定时任务（每天上午9点执行一次，提前7天提醒）
INSERT INTO `sys_job` (
    `job_id`,
    `job_name`,
    `job_group`,
    `invoke_target`,
    `cron_expression`,
    `misfire_policy`,
    `concurrent`,
    `status`,
    `create_by`,
    `create_time`,
    `update_by`,
    `update_time`,
    `remark`
) VALUES (
    101,
    '商家即将到期提醒',
    'SYSTEM',
    'merchantTask.checkExpiringSoonMerchants',
    '0 0 9 * * ?',
    '3',
    '1',
    '0',
    'admin',
    NOW(),
    '',
    NULL,
    '每天上午9点执行一次，检查即将到期的商家并发送提醒'
);

-- 查询插入结果
SELECT * FROM `sys_job` WHERE `job_name` IN ('商家过期状态检查', '商家即将到期提醒');
