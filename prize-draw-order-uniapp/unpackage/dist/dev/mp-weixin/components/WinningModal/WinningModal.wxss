@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.winning-modal-overlay.data-v-94e056b8 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  -webkit-animation: fadeIn-data-v-94e056b8 0.3s ease-out;
          animation: fadeIn-data-v-94e056b8 0.3s ease-out;
}
.winning-modal.data-v-94e056b8 {
  /* 背景色通过内联样式动态设置 */
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 90%;
  position: relative;
  -webkit-animation: modalSlideIn-data-v-94e056b8 0.4s ease-out;
          animation: modalSlideIn-data-v-94e056b8 0.4s ease-out;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}
.close-btn.data-v-94e056b8 {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.close-icon.data-v-94e056b8 {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}
.winning-animation.data-v-94e056b8 {
  text-align: center;
  position: relative;
}
.fireworks.data-v-94e056b8 {
  position: absolute;
  top: -20rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  width: 200rpx;
  height: 200rpx;
}
.firework.data-v-94e056b8 {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8rpx;
  height: 8rpx;
  background: #fff;
  border-radius: 50%;
  -webkit-animation: fireworkAnimation-data-v-94e056b8 1.5s ease-out infinite;
          animation: fireworkAnimation-data-v-94e056b8 1.5s ease-out infinite;
  -webkit-transform-origin: 0 0;
          transform-origin: 0 0;
}
.winning-icon.data-v-94e056b8 {
  margin: 40rpx 0 20rpx;
  -webkit-animation: bounce-data-v-94e056b8 1s ease-in-out infinite;
          animation: bounce-data-v-94e056b8 1s ease-in-out infinite;
}
.icon-text.data-v-94e056b8 {
  font-size: 120rpx;
  line-height: 1;
}
.congratulations.data-v-94e056b8 {
  margin-bottom: 30rpx;
}
.congrats-text.data-v-94e056b8 {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}
.prize-info.data-v-94e056b8 {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
}
.prize-name.data-v-94e056b8 {
  color: #333;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}
.prize-desc.data-v-94e056b8 {
  color: #666;
  font-size: 28rpx;
}
.draw-time.data-v-94e056b8 {
  margin: 20rpx 0 40rpx;
}
.draw-time text.data-v-94e056b8 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}
.claim-instruction.data-v-94e056b8 {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
}
.instruction-title.data-v-94e056b8 {
  color: #333;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}
.instruction-content.data-v-94e056b8 {
  color: #666;
  font-size: 26rpx;
  line-height: 1.5;
}
.wechat-qrcode.data-v-94e056b8 {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  text-align: center;
}
.qrcode-title.data-v-94e056b8 {
  color: #333;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.qrcode-img.data-v-94e056b8 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}
.qrcode-desc.data-v-94e056b8 {
  color: #666;
  font-size: 24rpx;
  line-height: 1.4;
}
.countdown.data-v-94e056b8 {
  text-align: center;
  margin-top: 20rpx;
}
.countdown text.data-v-94e056b8 {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
}
/* 动画效果 */
@-webkit-keyframes fadeIn-data-v-94e056b8 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@keyframes fadeIn-data-v-94e056b8 {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
@-webkit-keyframes modalSlideIn-data-v-94e056b8 {
from {
    -webkit-transform: scale(0.8) translateY(-50rpx);
            transform: scale(0.8) translateY(-50rpx);
    opacity: 0;
}
to {
    -webkit-transform: scale(1) translateY(0);
            transform: scale(1) translateY(0);
    opacity: 1;
}
}
@keyframes modalSlideIn-data-v-94e056b8 {
from {
    -webkit-transform: scale(0.8) translateY(-50rpx);
            transform: scale(0.8) translateY(-50rpx);
    opacity: 0;
}
to {
    -webkit-transform: scale(1) translateY(0);
            transform: scale(1) translateY(0);
    opacity: 1;
}
}
@-webkit-keyframes bounce-data-v-94e056b8 {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-20rpx);
            transform: translateY(-20rpx);
}
60% {
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
}
@keyframes bounce-data-v-94e056b8 {
0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-20rpx);
            transform: translateY(-20rpx);
}
60% {
    -webkit-transform: translateY(-10rpx);
            transform: translateY(-10rpx);
}
}
@-webkit-keyframes fireworkAnimation-data-v-94e056b8 {
0% {
    -webkit-transform: translate(0, 0) scale(1);
            transform: translate(0, 0) scale(1);
    opacity: 1;
}
100% {
    -webkit-transform: translate(100rpx, -100rpx) scale(0);
            transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
}
}
@keyframes fireworkAnimation-data-v-94e056b8 {
0% {
    -webkit-transform: translate(0, 0) scale(1);
            transform: translate(0, 0) scale(1);
    opacity: 1;
}
100% {
    -webkit-transform: translate(100rpx, -100rpx) scale(0);
            transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
}
}
