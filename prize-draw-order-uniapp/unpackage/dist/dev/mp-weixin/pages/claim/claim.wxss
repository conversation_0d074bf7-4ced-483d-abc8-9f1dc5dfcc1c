@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.claim-container.data-v-011e7c40 {
  min-height: 100vh;
  /* 背景色通过内联样式动态设置 */
  padding: 40rpx;
}
.loading-container.data-v-011e7c40,
.error-container.data-v-011e7c40 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}
.loading-icon.data-v-011e7c40,
.error-icon.data-v-011e7c40 {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}
.loading-text.data-v-011e7c40,
.error-text.data-v-011e7c40 {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40rpx;
}
.claim-content.data-v-011e7c40 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
}
.prize-section.data-v-011e7c40 {
  text-align: center;
  margin-bottom: 40rpx;
  padding-bottom: 40rpx;
  border-bottom: 1px solid #eee;
}
.prize-icon.data-v-011e7c40 {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}
.prize-title.data-v-011e7c40 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.prize-name.data-v-011e7c40 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.prize-desc.data-v-011e7c40 {
  font-size: 28rpx;
  color: #999;
}
.record-info.data-v-011e7c40 {
  margin-bottom: 40rpx;
}
.info-item.data-v-011e7c40 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1px solid #f5f5f5;
}
.label.data-v-011e7c40 {
  font-size: 28rpx;
  color: #666;
}
.value.data-v-011e7c40 {
  font-size: 28rpx;
  color: #333;
}
.status.claimed.data-v-011e7c40 {
  color: #52c41a;
}
.claim-instruction.data-v-011e7c40 {
  margin-bottom: 40rpx;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}
.instruction-title.data-v-011e7c40 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.instruction-content.data-v-011e7c40 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.wechat-qrcode.data-v-011e7c40 {
  text-align: center;
  margin-bottom: 40rpx;
}
.qrcode-title.data-v-011e7c40 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}
.qrcode-img.data-v-011e7c40 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}
.qrcode-desc.data-v-011e7c40 {
  color: #666;
  font-size: 24rpx;
  line-height: 1.4;
}
.qrcode-error.data-v-011e7c40 {
  color: #ff4757;
  font-size: 24rpx;
  padding: 20rpx;
  background: rgba(255, 71, 87, 0.1);
  border-radius: 8rpx;
  margin-top: 15rpx;
}
.action-buttons.data-v-011e7c40 {
  display: flex;
  gap: 20rpx;
}
.btn.data-v-011e7c40 {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
  font-weight: bold;
}
.btn-primary.data-v-011e7c40 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}
.btn-secondary.data-v-011e7c40 {
  background: #f5f5f5;
  color: #666;
}
