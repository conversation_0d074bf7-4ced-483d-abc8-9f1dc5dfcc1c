@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.container.data-v-57280228 {
  min-height: 100vh;
  position: relative;
  display: flex;
  flex-direction: column;
  padding-bottom: 160rpx;
  /* 为底部固定按钮留出空间 */
}
.main-content.data-v-57280228 {
  flex: 1;
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2;
  position: relative;
  min-height: calc(100vh - 160rpx);
  height: calc(100vh - 160rpx);
}
.logo-container.data-v-57280228 {
  margin-bottom: 30rpx;
}
.logo-container .logo-img.data-v-57280228 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  padding: 10rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}
.merchant-info.data-v-57280228 {
  text-align: center;
  width: 80%;
  margin: 30rpx 0;
  background: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.1);
}
.merchant-info .merchant-name.data-v-57280228 {
  font-size: 42rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}
.merchant-info .merchant-address.data-v-57280228 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 15rpx;
}
.merchant-info .table-info .table-number.data-v-57280228 {
  font-size: 28rpx;
  color: #888;
  background: rgba(102, 126, 234, 0.1);
  padding: 15rpx 30rpx;
  border-radius: 50rpx;
  display: inline-block;
}
.fixed-bottom-buttons.data-v-57280228 {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  gap: 20rpx;
}
.fixed-bottom-buttons .function-btn.data-v-57280228 {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 25rpx;
  padding: 30rpx 20rpx;
  text-align: center;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}
.fixed-bottom-buttons .function-btn.data-v-57280228:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.fixed-bottom-buttons .function-btn.disabled.data-v-57280228 {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: 0 4rpx 15rpx rgba(153, 153, 153, 0.3);
}
.fixed-bottom-buttons .function-btn.disabled .btn-text.data-v-57280228 {
  color: #666;
}
.fixed-bottom-buttons .function-btn .btn-icon.data-v-57280228 {
  font-size: 50rpx;
  margin-bottom: 10rpx;
}
.fixed-bottom-buttons .function-btn .btn-text.data-v-57280228 {
  font-size: 28rpx;
  font-weight: bold;
  color: #fff;
}
.fixed-bottom-buttons .order-btn.data-v-57280228 {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  box-shadow: 0 4rpx 15rpx rgba(255, 154, 158, 0.3);
}
.fixed-bottom-buttons .order-btn.disabled.data-v-57280228 {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  box-shadow: 0 4rpx 15rpx rgba(153, 153, 153, 0.3);
}
.fixed-bottom-buttons .lottery-btn.data-v-57280228 {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  box-shadow: 0 4rpx 15rpx rgba(168, 237, 234, 0.3);
}
.welcome-overlay.data-v-57280228 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 160rpx;
  background: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  padding: 20rpx;
}
.welcome-overlay .overlay-content.data-v-57280228 {
  background: transparent;
  border-radius: 30rpx;
  width: 90%;
  max-width: 600rpx;
  text-align: center;
  position: relative;
}
.welcome-overlay .overlay-content .welcome-text.data-v-57280228 {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
  text-align: center;
}
.welcome-overlay .overlay-content .countdown-text.data-v-57280228 {
  font-size: 24rpx;
  color: #ffffff;
  margin-top: 20rpx;
  display: block;
  text-align: center;
}
.welcome-overlay .overlay-content .overlay-welcome-text.data-v-57280228 {
  font-size: 42rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 30rpx;
  line-height: 1.4;
  background: rgba(255, 255, 255, 0.95);
  padding: 30rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.welcome-overlay .overlay-content .overlay-image-container.data-v-57280228 {
  width: 100%;
  background: transparent;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.welcome-overlay .overlay-content .overlay-image-container .overlay-image.data-v-57280228 {
  width: 100%;
  height: 500rpx;
  border-radius: 15rpx;
  display: block;
  margin: 0 auto;
}
.welcome-overlay .overlay-content .overlay-countdown.data-v-57280228 {
  font-size: 26rpx;
  color: #666;
  margin-top: 20rpx;
  background: rgba(255, 255, 255, 0.95);
  padding: 15rpx 25rpx;
  border-radius: 20rpx;
  display: inline-block;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.15);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.expired-content.data-v-57280228 {
  text-align: center;
  padding: 40rpx 20rpx;
}
.expired-content .expired-icon.data-v-57280228 {
  font-size: 80rpx;
  margin-bottom: 30rpx;
}
.expired-content .expired-text.data-v-57280228 {
  font-size: 32rpx;
  color: #666;
  line-height: 1.6;
}
