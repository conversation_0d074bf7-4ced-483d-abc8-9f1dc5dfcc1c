@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 全局样式 - 确保页面背景完全覆盖 */
page {
  height: 100%;
  background: transparent;
}
@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.records-page.data-v-00c304e8 {
  min-height: 100vh;
  height: auto;
  /* 背景色通过内联样式动态设置 */
  padding-bottom: 20rpx;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
/* 筛选标签 */
.filter-tabs.data-v-00c304e8 {
  display: flex;
  background: rgba(255, 255, 255, 0.95);
  margin: 20rpx 30rpx 20rpx;
  border-radius: 25rpx;
  padding: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.filter-tabs .tab-item.data-v-00c304e8 {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  border-radius: 20rpx;
  position: relative;
  transition: all 0.3s ease;
}
.filter-tabs .tab-item text.data-v-00c304e8 {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}
.filter-tabs .tab-item .badge.data-v-00c304e8 {
  position: absolute;
  top: 10rpx;
  right: 15rpx;
  background: #ff4757;
  color: white;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 10rpx;
  min-width: 20rpx;
  text-align: center;
}
.filter-tabs .tab-item.active.data-v-00c304e8 {
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);
}
.filter-tabs .tab-item.active text.data-v-00c304e8 {
  color: white;
  font-weight: bold;
}
.filter-tabs .tab-item.active .badge.data-v-00c304e8 {
  background: #ff6b7a;
}
/* 加载状态 */
.loading-container.data-v-00c304e8 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}
.loading-container .loading-spinner.data-v-00c304e8 {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid #ffffff;
  border-radius: 50%;
  -webkit-animation: spin-data-v-00c304e8 1s linear infinite;
          animation: spin-data-v-00c304e8 1s linear infinite;
  margin-bottom: 20rpx;
}
.loading-container .loading-text.data-v-00c304e8 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 28rpx;
}
@-webkit-keyframes spin-data-v-00c304e8 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes spin-data-v-00c304e8 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
/* 记录容器 */
.records-container.data-v-00c304e8 {
  padding: 0 30rpx;
}
/* 空状态 */
.empty-state.data-v-00c304e8 {
  text-align: center;
  padding: 100rpx 40rpx;
}
.empty-state .empty-image.data-v-00c304e8 {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
  opacity: 0.6;
}
.empty-state .empty-title.data-v-00c304e8 {
  display: block;
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: bold;
  margin-bottom: 15rpx;
}
.empty-state .empty-subtitle.data-v-00c304e8 {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 40rpx;
}
.empty-state .empty-action .btn.data-v-00c304e8 {
  display: inline-block;
  padding: 25rpx 50rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
}
.empty-state .empty-action .btn.btn-primary.data-v-00c304e8 {
  background: linear-gradient(135deg, #ff6b7a, #ff8e9b);
  color: white;
  box-shadow: 0 8rpx 25rpx rgba(255, 107, 122, 0.4);
}
/* 记录列表 */
.records-list .record-item.data-v-00c304e8 {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}
.records-list .record-item.data-v-00c304e8:active {
  -webkit-transform: scale(0.98);
          transform: scale(0.98);
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);
}
.records-list .record-item .record-card.data-v-00c304e8 {
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.records-list .record-item .record-card .prize-info.data-v-00c304e8 {
  display: flex;
  align-items: center;
  flex: 1;
}
.records-list .record-item .record-card .prize-info .prize-icon.data-v-00c304e8 {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  border-radius: 15rpx;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}
.records-list .record-item .record-card .prize-info .prize-icon .icon-img.data-v-00c304e8 {
  width: 60rpx;
  height: 60rpx;
}
.records-list .record-item .record-card .prize-info .prize-details.data-v-00c304e8 {
  flex: 1;
}
.records-list .record-item .record-card .prize-info .prize-details .prize-name.data-v-00c304e8 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.records-list .record-item .record-card .prize-info .prize-details .prize-desc.data-v-00c304e8 {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
  line-height: 1.4;
}
.records-list .record-item .record-card .prize-info .prize-details .draw-time.data-v-00c304e8 {
  display: block;
  font-size: 24rpx;
  color: #999;
}
.records-list .record-item .record-card .status-section.data-v-00c304e8 {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15rpx;
}
.records-list .record-item .record-card .status-section .status-badge.data-v-00c304e8 {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.records-list .record-item .record-card .status-section .status-badge .status-text.data-v-00c304e8 {
  font-weight: 500;
}
.records-list .record-item .record-card .status-section .status-badge.won.data-v-00c304e8 {
  background: linear-gradient(135deg, #ff6b7a, #ff8e9b);
  color: white;
}
.records-list .record-item .record-card .status-section .status-badge.claimed.data-v-00c304e8 {
  background: linear-gradient(135deg, #2ed573, #7bed9f);
  color: white;
}
.records-list .record-item .record-card .status-section .status-badge.unclaimed.data-v-00c304e8 {
  background: linear-gradient(135deg, #ffa726, #ffcc02);
  color: white;
  -webkit-animation: pulse-data-v-00c304e8 2s infinite;
          animation: pulse-data-v-00c304e8 2s infinite;
}
.records-list .record-item .record-card .status-section .status-badge.thanks.data-v-00c304e8 {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}
.records-list .record-item .record-card .status-section .status-badge.not-won.data-v-00c304e8 {
  background: #f1f2f6;
  color: #666;
}
.records-list .record-item .activity-info.data-v-00c304e8 {
  padding: 0 30rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;
  margin-top: 10rpx;
  padding-top: 20rpx;
}
.records-list .record-item .activity-info .activity-name.data-v-00c304e8 {
  font-size: 24rpx;
  color: #666;
  font-style: italic;
}
@-webkit-keyframes pulse-data-v-00c304e8 {
0% {
    box-shadow: 0 0 0 0 rgba(255, 167, 38, 0.7);
}
70% {
    box-shadow: 0 0 0 10rpx rgba(255, 167, 38, 0);
}
100% {
    box-shadow: 0 0 0 0 rgba(255, 167, 38, 0);
}
}
@keyframes pulse-data-v-00c304e8 {
0% {
    box-shadow: 0 0 0 0 rgba(255, 167, 38, 0.7);
}
70% {
    box-shadow: 0 0 0 10rpx rgba(255, 167, 38, 0);
}
100% {
    box-shadow: 0 0 0 0 rgba(255, 167, 38, 0);
}
}
/* 刷新提示 */
.refresh-tip.data-v-00c304e8 {
  text-align: center;
  padding: 20rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 26rpx;
}
/* 底部提示 */
.bottom-tip.data-v-00c304e8 {
  text-align: center;
  padding: 40rpx 20rpx;
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
}
/* 响应式适配 */
@media screen and (max-width: 750rpx) {
.record-card .prize-info .prize-icon.data-v-00c304e8 {
    width: 70rpx;
    height: 70rpx;
    margin-right: 15rpx;
}
.record-card .prize-info .prize-icon .icon-img.data-v-00c304e8 {
    width: 50rpx;
    height: 50rpx;
}
.record-card .prize-info .prize-details .prize-name.data-v-00c304e8 {
    font-size: 30rpx;
}
.record-card .prize-info .prize-details .prize-desc.data-v-00c304e8 {
    font-size: 22rpx;
}
.record-card .prize-info .prize-details .draw-time.data-v-00c304e8 {
    font-size: 22rpx;
}
.record-card .status-section .status-badge.data-v-00c304e8 {
    font-size: 22rpx;
    padding: 6rpx 12rpx;
}
.record-card .status-section .claim-btn.data-v-00c304e8 {
    font-size: 22rpx;
    padding: 10rpx 20rpx;
}
}
