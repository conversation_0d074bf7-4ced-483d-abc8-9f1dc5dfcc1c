{"version": 3, "sources": [null, "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/components/WinningModal/WinningModal.vue?aa4f", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/components/WinningModal/WinningModal.vue?0a0c", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/components/WinningModal/WinningModal.vue?4620", "uni-app:///components/WinningModal/WinningModal.vue", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/components/WinningModal/WinningModal.vue?ecd2", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/components/WinningModal/WinningModal.vue?366a"], "names": ["name", "props", "visible", "type", "default", "result", "activityInfo", "autoClose", "autoCloseDelay", "merchantConfig", "data", "countdown", "timer", "countdownTimer", "imageLoadError", "computed", "uiConfig", "console", "primaryColor", "backgroundGradient", "fireworkStyles", "transform", "animationDelay", "watch", "methods", "startAutoClose", "clearInterval", "clearTimers", "clearTimeout", "closeModal", "handleOverlayClick", "formatTime", "getFullImageUrl", "handleImageError", "handleImageLoad", "adjustColor", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtBA;AAAA;AAAA;AAAA;AAA00B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC8D91B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QAAA;MAAA;IACA;IACAE;MACAH;MACAC;QAAA;MAAA;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;;IACAK;MACAN;MACAC;QAAA;MAAA;IACA;EACA;EACAM;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;UACA;QACA;UACAC;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA;UACAC;UACAC;QACA;MAAA;IACA;EACA;EACAC;IACArB;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAsB;IACAC;MAAA;MACA;MAEA;;MAEA;MACA;QACA;QACA;UACAC;QACA;MACA;;MAEA;MACA;QACA;MACA;IACA;IAEAC;MACA;QACAC;QACA;MACA;MACA;QACAF;QACA;MACA;MACA;IACA;IAEAG;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAIAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAhB;MACA;IACA;IAEA;IACAiB;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;EACA;EAEAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACzOA;AAAA;AAAA;AAAA;AAAikD,CAAgB,i8CAAG,EAAC,C;;;;;;;;;;;ACArlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/WinningModal/WinningModal.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./WinningModal.vue?vue&type=template&id=94e056b8&scoped=true&\"\nvar renderjs\nimport script from \"./WinningModal.vue?vue&type=script&lang=js&\"\nexport * from \"./WinningModal.vue?vue&type=script&lang=js&\"\nimport style0 from \"./WinningModal.vue?vue&type=style&index=0&id=94e056b8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"94e056b8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/WinningModal/WinningModal.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./WinningModal.vue?vue&type=template&id=94e056b8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.visible ? _vm.formatTime(_vm.result.drawTime) : null\n  var m1 =\n    _vm.visible && _vm.activityInfo && _vm.activityInfo.wechatQrcode\n      ? _vm.getFullImageUrl(_vm.activityInfo.wechatQrcode)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./WinningModal.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./WinningModal.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"winning-modal-overlay\" v-if=\"visible\" @click=\"handleOverlayClick\">\n    <view class=\"winning-modal\" :style=\"{ background: backgroundGradient }\" @click.stop>\n      <!-- 关闭按钮 -->\n      <view class=\"close-btn\" @click=\"closeModal\">\n        <text class=\"close-icon\">×</text>\n      </view>\n\n      <!-- 中奖动画效果 -->\n      <view class=\"winning-animation\">\n        <view class=\"fireworks\">\n          <view class=\"firework\" v-for=\"(style, index) in fireworkStyles\" :key=\"index\" :style=\"style\"></view>\n        </view>\n\n        <!-- 中奖图标 -->\n        <view class=\"winning-icon\">\n          <text class=\"icon-text\">🎉</text>\n        </view>\n\n        <!-- 恭喜文字 -->\n        <view class=\"congratulations\">\n          <text class=\"congrats-text\">恭喜中奖！</text>\n        </view>\n\n        <!-- 奖品信息 -->\n        <view class=\"prize-info\">\n          <view class=\"prize-name\">{{ result.prizeName }}</view>\n          <view class=\"prize-desc\" v-if=\"result.prizeDesc\">{{ result.prizeDesc }}</view>\n        </view>\n\n        <!-- 中奖时间 -->\n        <view class=\"draw-time\">\n          <text>抽奖时间：{{ formatTime(result.drawTime) }}</text>\n        </view>\n\n        <!-- 领取说明 -->\n        <view class=\"claim-instruction\" v-if=\"activityInfo && activityInfo.claimInstruction\">\n          <view class=\"instruction-title\">领取说明</view>\n          <view class=\"instruction-content\">{{ activityInfo.claimInstruction }}</view>\n        </view>\n\n        <!-- 微信二维码 -->\n        <view class=\"wechat-qrcode\" v-if=\"activityInfo && activityInfo.wechatQrcode\">\n          <image :src=\"getFullImageUrl(activityInfo.wechatQrcode)\" class=\"qrcode-img\" mode=\"aspectFit\"\n            @error=\"handleImageError\" @load=\"handleImageLoad\"></image>\n          <view class=\"qrcode-error\" v-if=\"imageLoadError\">\n            <text>二维码加载失败</text>\n          </view>\n        </view>\n\n\n      </view>\n\n      <!-- 自动关闭倒计时 -->\n      <view class=\"countdown\" v-if=\"countdown > 0\">\n        <text>{{ countdown }}秒后自动关闭</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getImageUrl } from '@/utils/api.js'\n\nexport default {\n  name: 'WinningModal',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    result: {\n      type: Object,\n      default: () => ({})\n    },\n    activityInfo: {\n      type: Object,\n      default: () => ({})\n    },\n    autoClose: {\n      type: Boolean,\n      default: true\n    },\n    autoCloseDelay: {\n      type: Number,\n      default: 5000 // 5秒\n    },\n    merchantConfig: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  data() {\n    return {\n      countdown: 0,\n      timer: null,\n      countdownTimer: null,\n      imageLoadError: false\n    }\n  },\n\n  computed: {\n    // 解析UI配置\n    uiConfig() {\n      const uiConfigStr = this.merchantConfig.ui_config\n      if (uiConfigStr) {\n        try {\n          return JSON.parse(uiConfigStr)\n        } catch (e) {\n          console.error('UI配置解析失败:', e)\n          return {}\n        }\n      }\n      return {}\n    },\n\n    // 主题色彩\n    primaryColor() {\n      return this.uiConfig.primaryColor || '#667eea'\n    },\n\n    // 背景渐变色\n    backgroundGradient() {\n      const color = this.primaryColor\n      // 生成基于主题色的渐变背景\n      return `linear-gradient(135deg, ${color} 0%, ${this.adjustColor(color, -20)} 100%)`\n    },\n\n    // 烟花样式数组\n    fireworkStyles() {\n      const angles = [0, 60, 120, 180, 240, 300]\n      return angles.map((angle, index) => ({\n        transform: `rotate(${angle}deg)`,\n        animationDelay: `${(index + 1) * 0.1}s`\n      }))\n    }\n  },\n  watch: {\n    visible(newVal) {\n      if (newVal) {\n        this.startAutoClose()\n      } else {\n        this.clearTimers()\n      }\n    }\n  },\n  methods: {\n    startAutoClose() {\n      if (!this.autoClose) return\n\n      this.countdown = Math.ceil(this.autoCloseDelay / 1000)\n\n      // 倒计时显示\n      this.countdownTimer = setInterval(() => {\n        this.countdown--\n        if (this.countdown <= 0) {\n          clearInterval(this.countdownTimer)\n        }\n      }, 1000)\n\n      // 自动关闭定时器\n      this.timer = setTimeout(() => {\n        this.closeModal()\n      }, this.autoCloseDelay)\n    },\n\n    clearTimers() {\n      if (this.timer) {\n        clearTimeout(this.timer)\n        this.timer = null\n      }\n      if (this.countdownTimer) {\n        clearInterval(this.countdownTimer)\n        this.countdownTimer = null\n      }\n      this.countdown = 0\n    },\n\n    closeModal() {\n      this.clearTimers()\n      this.$emit('close')\n    },\n\n    handleOverlayClick() {\n      // 点击遮罩层关闭弹窗\n      this.closeModal()\n    },\n\n\n\n    formatTime(timeStr) {\n      if (!timeStr) return ''\n      const date = new Date(timeStr)\n      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`\n    },\n\n    // 获取完整的图片URL\n    getFullImageUrl(imagePath) {\n      return getImageUrl(imagePath)\n    },\n\n    // 图片加载错误处理\n    handleImageError() {\n      console.error('微信二维码图片加载失败')\n      this.imageLoadError = true\n    },\n\n    // 图片加载成功处理\n    handleImageLoad() {\n      this.imageLoadError = false\n    },\n\n    // 颜色调整工具方法\n    adjustColor(color, amount) {\n      // 将十六进制颜色转换为RGB\n      const hex = color.replace('#', '')\n      const r = parseInt(hex.substr(0, 2), 16)\n      const g = parseInt(hex.substr(2, 2), 16)\n      const b = parseInt(hex.substr(4, 2), 16)\n\n      // 调整亮度\n      const newR = Math.max(0, Math.min(255, r + amount))\n      const newG = Math.max(0, Math.min(255, g + amount))\n      const newB = Math.max(0, Math.min(255, b + amount))\n\n      // 转换回十六进制\n      return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`\n    }\n  },\n\n  beforeDestroy() {\n    this.clearTimers()\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.winning-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.6);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.winning-modal {\n  /* 背景色通过内联样式动态设置 */\n  border-radius: 20rpx;\n  padding: 60rpx 40rpx 40rpx;\n  margin: 40rpx;\n  max-width: 600rpx;\n  width: 90%;\n  position: relative;\n  animation: modalSlideIn 0.4s ease-out;\n  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);\n}\n\n.close-btn {\n  position: absolute;\n  top: 20rpx;\n  right: 20rpx;\n  width: 60rpx;\n  height: 60rpx;\n  background: rgba(255, 255, 255, 0.2);\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10;\n}\n\n.close-icon {\n  color: #fff;\n  font-size: 40rpx;\n  font-weight: bold;\n}\n\n.winning-animation {\n  text-align: center;\n  position: relative;\n}\n\n.fireworks {\n  position: absolute;\n  top: -20rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  width: 200rpx;\n  height: 200rpx;\n}\n\n.firework {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 8rpx;\n  height: 8rpx;\n  background: #fff;\n  border-radius: 50%;\n  animation: fireworkAnimation 1.5s ease-out infinite;\n  transform-origin: 0 0;\n}\n\n.winning-icon {\n  margin: 40rpx 0 20rpx;\n  animation: bounce 1s ease-in-out infinite;\n}\n\n.icon-text {\n  font-size: 120rpx;\n  line-height: 1;\n}\n\n.congratulations {\n  margin-bottom: 30rpx;\n}\n\n.congrats-text {\n  color: #fff;\n  font-size: 48rpx;\n  font-weight: bold;\n  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);\n}\n\n.prize-info {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 15rpx;\n  padding: 30rpx;\n  margin: 30rpx 0;\n}\n\n.prize-name {\n  color: #333;\n  font-size: 36rpx;\n  font-weight: bold;\n  margin-bottom: 10rpx;\n}\n\n.prize-desc {\n  color: #666;\n  font-size: 28rpx;\n}\n\n.draw-time {\n  margin: 20rpx 0 40rpx;\n}\n\n.draw-time text {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 24rpx;\n}\n\n.claim-instruction {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 15rpx;\n  padding: 30rpx;\n  margin: 30rpx 0;\n}\n\n.instruction-title {\n  color: #333;\n  font-size: 30rpx;\n  font-weight: bold;\n  margin-bottom: 15rpx;\n}\n\n.instruction-content {\n  color: #666;\n  font-size: 26rpx;\n  line-height: 1.5;\n}\n\n.wechat-qrcode {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 15rpx;\n  padding: 30rpx;\n  margin: 30rpx 0;\n  text-align: center;\n}\n\n.qrcode-title {\n  color: #333;\n  font-size: 30rpx;\n  font-weight: bold;\n  margin-bottom: 20rpx;\n}\n\n.qrcode-img {\n  width: 200rpx;\n  height: 200rpx;\n  border-radius: 10rpx;\n  margin-bottom: 15rpx;\n}\n\n.qrcode-desc {\n  color: #666;\n  font-size: 24rpx;\n  line-height: 1.4;\n}\n\n\n\n.countdown {\n  text-align: center;\n  margin-top: 20rpx;\n}\n\n.countdown text {\n  color: rgba(255, 255, 255, 0.7);\n  font-size: 24rpx;\n}\n\n/* 动画效果 */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes modalSlideIn {\n  from {\n    transform: scale(0.8) translateY(-50rpx);\n    opacity: 0;\n  }\n\n  to {\n    transform: scale(1) translateY(0);\n    opacity: 1;\n  }\n}\n\n@keyframes bounce {\n\n  0%,\n  20%,\n  50%,\n  80%,\n  100% {\n    transform: translateY(0);\n  }\n\n  40% {\n    transform: translateY(-20rpx);\n  }\n\n  60% {\n    transform: translateY(-10rpx);\n  }\n}\n\n@keyframes fireworkAnimation {\n  0% {\n    transform: translate(0, 0) scale(1);\n    opacity: 1;\n  }\n\n  100% {\n    transform: translate(100rpx, -100rpx) scale(0);\n    opacity: 0;\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./WinningModal.vue?vue&type=style&index=0&id=94e056b8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./WinningModal.vue?vue&type=style&index=0&id=94e056b8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754142125255\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}