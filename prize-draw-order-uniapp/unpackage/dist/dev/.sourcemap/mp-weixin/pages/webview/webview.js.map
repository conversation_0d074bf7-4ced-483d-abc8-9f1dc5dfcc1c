{"version": 3, "sources": ["uni-app:///main.js", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/webview/webview.vue?84f3", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/webview/webview.vue?67ca", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/webview/webview.vue?689a", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/webview/webview.vue?3462", "uni-app:///pages/webview/webview.vue", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/webview/webview.vue?f656", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/webview/webview.vue?d833"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "url", "merchantCode", "merchantConfig", "computed", "uiConfig", "console", "primaryColor", "watch", "handler", "uni", "frontColor", "backgroundColor", "immediate", "onLoad", "methods", "initPage", "title", "loadMerchantInfo", "config<PERSON>pi", "res", "handleMessage"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACOz1B;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;UACA;QACA;UACAC;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACAD;MACAE;QACA;UACAC;YACAC;YACAC;UACA;QACA;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACA;IAEA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAEA;gBACAN;kBACAO;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAX;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAd;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAe;MACAf;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5FA;AAAA;AAAA;AAAA;AAA4jD,CAAgB,47CAAG,EAAC,C;;;;;;;;;;;ACAhlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/webview/webview.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/webview/webview.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./webview.vue?vue&type=template&id=baadaa0c&scoped=true&\"\nvar renderjs\nimport script from \"./webview.vue?vue&type=script&lang=js&\"\nexport * from \"./webview.vue?vue&type=script&lang=js&\"\nimport style0 from \"./webview.vue?vue&type=style&index=0&id=baadaa0c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"baadaa0c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/webview/webview.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=template&id=baadaa0c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"webview-container\">\n    <web-view :src=\"url\" @message=\"handleMessage\"></web-view>\n  </view>\n</template>\n\n<script>\nimport { configApi } from '@/utils/api.js'\n\nexport default {\n  data() {\n    return {\n      url: '',\n      merchantCode: '',\n      merchantConfig: {}\n    }\n  },\n\n  computed: {\n    // 解析UI配置\n    uiConfig() {\n      const uiConfigStr = this.merchantConfig.ui_config\n      if (uiConfigStr) {\n        try {\n          return JSON.parse(uiConfigStr)\n        } catch (e) {\n          console.error('UI配置解析失败:', e)\n          return {}\n        }\n      }\n      return {}\n    },\n\n    // 主题色彩\n    primaryColor() {\n      return this.uiConfig.primaryColor || '#667eea'\n    }\n  },\n\n  watch: {\n    // 监听主题色彩变化，动态设置导航栏颜色\n    primaryColor: {\n      handler(newColor) {\n        if (newColor) {\n          uni.setNavigationBarColor({\n            frontColor: '#ffffff',\n            backgroundColor: newColor\n          })\n        }\n      },\n      immediate: true\n    }\n  },\n\n  onLoad(options) {\n    this.url = decodeURIComponent(options.url || '')\n    this.merchantCode = options.merchantCode || 'system'\n\n    this.initPage()\n  },\n\n  methods: {\n    async initPage() {\n      try {\n        // 加载商家信息\n        await this.loadMerchantInfo()\n\n        // 设置导航栏标题\n        uni.setNavigationBarTitle({\n          title: '点餐'\n        })\n      } catch (error) {\n        console.error('页面初始化失败:', error)\n      }\n    },\n\n    async loadMerchantInfo() {\n      try {\n        const res = await configApi.getMerchantConfig(this.merchantCode)\n        if (res.code === 200) {\n          this.merchantConfig = res.data || {}\n        }\n      } catch (error) {\n        console.error('获取商家配置失败:', error)\n        this.merchantConfig = {}\n      }\n    },\n\n    handleMessage(event) {\n      console.log('WebView消息:', event.detail.data)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.webview-container {\n  height: 100vh;\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=style&index=0&id=baadaa0c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./webview.vue?vue&type=style&index=0&id=baadaa0c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754184105654\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}