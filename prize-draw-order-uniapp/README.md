# 扫码点餐抽奖系统 - UniApp 小程序

## 项目简介

这是一个基于 UniApp 开发的扫码点餐抽奖系统小程序，用户可以通过扫描桌台二维码参与抽奖活动，并跳转到美团小程序进行点餐。

## 功能特性

- 🎯 **扫码识别** - 扫描桌台二维码获取商家和活动信息
- 🎰 **九宫格抽奖** - 精美的九宫格动画和抽奖逻辑（已更新）
- 🎉 **中奖弹窗** - 精美的中奖弹窗，展示微信二维码和领取说明（已更新）
- 📱 **美团跳转** - 无缝跳转到美团小程序点餐
- 📋 **记录管理** - 用户抽奖记录和奖品管理
- 🎁 **奖品领取** - 完整的奖品领取流程，包含微信二维码展示
- 🎨 **精美 UI** - 渐变色彩和动画效果

## 项目结构

```
prize-draw-order-uniapp/
├── pages/                  # 页面文件
│   ├── index/             # 首页 - 扫码入口
│   ├── lottery/           # 抽奖页面 - 九宫格抽奖
│   ├── result/            # 结果页面 - 抽奖结果展示
│   ├── records/           # 记录页面 - 用户抽奖记录
│   ├── claim/             # 领取页面 - 奖品领取
│   └── test-modal/        # 弹窗测试页面（新增）
├── components/            # 组件文件（新增）
│   └── WinningModal/      # 中奖弹窗组件（新增）
├── static/                # 静态资源
├── utils/                 # 工具类
├── App.vue               # 应用入口组件
├── main.js               # 主入口文件
├── pages.json            # 页面路由配置
├── manifest.json         # 应用配置文件
├── uni.scss              # 全局样式
└── package.json          # 项目依赖配置
```

## 开发环境

### 推荐方式：HBuilderX

1. **下载安装 HBuilderX**

   - 访问：https://www.dcloud.io/hbuilderx.html
   - 下载并安装 HBuilderX 标准版（免费）

2. **导入项目**

   - 打开 HBuilderX
   - 文件 → 导入 → 从本地目录导入
   - 选择项目根目录

3. **运行项目**
   - 右键项目根目录
   - 运行 → 运行到小程序模拟器 → 微信开发者工具

### 命令行方式

```bash
# 安装依赖
npm install

# 开发微信小程序
npm run dev:mp-weixin

# 构建微信小程序
npm run build:mp-weixin
```

## 配置说明

### 1. 小程序配置

在 `manifest.json` 中配置小程序 AppID：

```json
{
  "mp-weixin": {
    "appid": "your-mini-program-appid"
  }
}
```

### 2. API 接口配置

在 `utils/api.js` 中配置后端 API 地址。

## 九宫格抽奖功能

### 功能特点

- **3x3 九宫格布局**：中间为抽奖按钮，周围 8 个位置展示奖品
- **流畅动画效果**：跑马灯式的高亮动画，视觉效果佳
- **智能奖品配置**：自动适配后台设置的奖项，不足 8 个自动填充
- **剩余次数显示**：中心按钮实时显示剩余抽奖次数
- **响应式设计**：适配不同屏幕尺寸和设备

### 实现文件

- `pages/lottery/lottery.vue` - 主抽奖页面（已更新为九宫格）
- `pages/lottery/demo.vue` - 九宫格抽奖演示页面
- `lottery-preview.html` - 浏览器预览页面（可直接打开查看效果）

### 动画逻辑

1. 点击中心按钮开始抽奖
2. 九宫格按顺序高亮显示（跑马灯效果）
3. 转 3 圈后逐渐减速
4. 停在中奖奖品位置
5. 弹窗显示中奖结果

## 页面说明

- **首页 (index)** - 扫码入口、商家信息、活动介绍
- **抽奖页面 (lottery)** - 九宫格抽奖、奖品展示、抽奖规则
- **结果页面 (result)** - 中奖动画、奖品信息、领取说明
- **记录页面 (records)** - 中奖记录展示、多种筛选、待领取提醒、一键领取
- **领取页面 (claim)** - 奖品详情、领取表单、确认领取

## 技术栈

- **框架**：UniApp
- **语言**：Vue.js 2.x + JavaScript
- **样式**：Sass/SCSS
- **平台**：微信小程序 + H5

## 支持平台

- ✅ 微信小程序
- ✅ H5
- ⚠️ 其他平台（需要适配）

---

## 新增功能：中奖页面（微信二维码 + 领取说明）

### 功能描述

- 抽奖后如果中奖，会显示精美的中奖弹窗
- 弹窗包含恭喜动画、奖品信息、领取按钮等
- **新增：展示活动微信二维码，用户可扫码关注**
- **新增：显示详细的领取说明文本**
- 支持 5 秒后自动关闭
- 支持手动点击关闭按钮关闭
- 支持点击遮罩层关闭

### 组件位置

- 组件路径：`/components/WinningModal/WinningModal.vue`
- 测试页面：`/pages/test-modal/test-modal.vue`
- 说明文档：`/中奖页面功能说明.md`

### 使用方法

```vue
<template>
  <WinningModal
    :visible="showWinningModal"
    :result="currentWinningResult"
    :activityInfo="currentActivity"
    :autoClose="true"
    :autoCloseDelay="5000"
    @close="handleWinningModalClose"
    @claim="handleClaimPrize"
  />
</template>

<script>
import WinningModal from "@/components/WinningModal/WinningModal.vue";

export default {
  components: {
    WinningModal,
  },
  data() {
    return {
      showWinningModal: false,
      currentWinningResult: null,
      currentActivity: {
        claimInstruction: "请携带身份证到前台领取",
        wechatQrcode: "https://example.com/qrcode.jpg",
      },
    };
  },
  methods: {
    handleWinningModalClose() {
      this.showWinningModal = false;
    },
    handleClaimPrize(result) {
      // 跳转到领取页面
      uni.navigateTo({
        url: `/pages/claim/claim?recordId=${result.recordId}`,
      });
    },
  },
};
</script>
```

### 组件属性

- `visible`: 是否显示弹窗
- `result`: 中奖结果对象
- `activityInfo`: 活动信息对象（包含微信二维码和领取说明）
- `autoClose`: 是否自动关闭（默认 true）
- `autoCloseDelay`: 自动关闭延迟时间（默认 5000ms）

### 组件事件

- `@close`: 弹窗关闭事件
- `@claim`: 领取奖品事件

### 数据结构

#### activityInfo 对象

```javascript
{
  claimInstruction: '请携带身份证到前台领取奖品...',  // 领取说明
  wechatQrcode: 'https://example.com/qrcode.jpg'      // 微信二维码URL
}
```

### 测试方法

访问测试页面 `/pages/test-modal/test-modal` 可以测试弹窗效果，包括微信二维码和领取说明的显示。

---

## 新增功能：中奖记录页面

### 功能描述

- 展示用户的所有抽奖记录和中奖情况
- 支持多种筛选方式：全部记录、中奖记录、待领取
- 待领取奖品显示数量徽章和"立即领取"按钮
- 支持下拉刷新和点击查看详情
- 精美的卡片式布局和动画效果

### 页面特性

- **三种筛选标签**：全部记录、中奖记录、待领取
- **状态标识**：已领取（绿色）、待领取（橙色脉冲）、未中奖（灰色）
- **一键领取**：点击待领取记录直接跳转到领取页面
- **下拉刷新**：支持下拉刷新最新数据
- **空状态处理**：无记录时显示友好的空状态页面

### 页面位置

- 页面路径：`/pages/records/records.vue`
- 预览页面：`/records-preview.html`（浏览器预览）
- 说明文档：`/中奖记录页面功能说明.md`

### 跳转方式

```javascript
// 从抽奖页面跳转（带用户openid）
uni.navigateTo({
  url: `/pages/records/records?userOpenid=${userOpenid}`,
});

// 直接访问
uni.navigateTo({
  url: "/pages/records/records",
});
```

### 数据接口

使用以下 API 接口获取数据：

- `lotteryApi.getUserRecords(userOpenid)` - 获取全部记录
- `lotteryApi.getUserWinningRecords(userOpenid)` - 获取中奖记录
- `lotteryApi.getUserUnclaimedRecords(userOpenid)` - 获取待领取记录

### 预览效果

在浏览器中打开 `records-preview.html` 可以预览页面效果，包括：

- 渐变背景和毛玻璃效果
- 筛选标签和徽章显示
- 不同状态的记录卡片
- 待领取按钮的交互效果

---

**推荐使用 HBuilderX 进行开发！**
